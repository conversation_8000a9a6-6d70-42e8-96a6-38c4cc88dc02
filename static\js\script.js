// Global variables
let currentDownloadUrl = null;

// DOM elements - will be initialized after DOM loads
let uploadArea, fileInput, processingSection, resultsSection, errorSection, uploadSection;
let originalImage, processedImage, downloadBtn, errorMessage, toastContainer;

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    console.log('=== FACE SLIMMER AI INITIALIZED ===');

    // Initialize DOM elements after DOM is loaded
    uploadArea = document.getElementById('uploadArea');
    fileInput = document.getElementById('fileInput');
    processingSection = document.getElementById('processingSection');
    resultsSection = document.getElementById('resultsSection');
    errorSection = document.getElementById('errorSection'); // May not exist
    uploadSection = document.querySelector('.upload-section');
    originalImage = document.getElementById('originalImage');
    processedImage = document.getElementById('processedImage');
    downloadBtn = document.getElementById('downloadBtn');
    errorMessage = document.getElementById('errorMessage'); // May not exist
    toastContainer = document.getElementById('toastContainer');

    // Check if critical elements were found
    const criticalElements = {
        uploadArea, fileInput, processingSection, resultsSection,
        uploadSection, originalImage, processedImage, downloadBtn, toastContainer
    };

    const optionalElements = {
        errorSection, errorMessage
    };

    for (const [name, element] of Object.entries(criticalElements)) {
        if (!element) {
            console.error(`❌ Critical element not found: ${name}`);
        } else {
            console.log(`✅ Found critical element: ${name}`);
        }
    }

    for (const [name, element] of Object.entries(optionalElements)) {
        if (!element) {
            console.warn(`⚠️ Optional element not found: ${name}`);
        } else {
            console.log(`✅ Found optional element: ${name}`);
        }
    }

    initializeEventListeners();
    checkServerStatus();
});

function checkServerStatus() {
    console.log('Checking server status...');

    fetch('/debug')
        .then(response => response.json())
        .then(data => {
            console.log('Server status:', data);
            if (data.status) {
                console.log('✅ Server is running properly');
                showToast('Server connected successfully', 'success');
            }
        })
        .catch(error => {
            console.error('❌ Server status check failed:', error);
            showToast('Warning: Could not connect to server. Please ensure the Flask app is running.', 'error');
        });
}

function initializeEventListeners() {
    // File input change event
    fileInput.addEventListener('change', handleFileSelect);

    // Drag and drop events
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('dragleave', handleDragLeave);
    uploadArea.addEventListener('drop', handleDrop);
    uploadArea.addEventListener('click', () => fileInput.click());

    // Download button event
    downloadBtn.addEventListener('click', handleDownload);

    // Initialize comparison slider
    initializeComparisonSlider();

    // Prevent default drag behaviors on the document
    document.addEventListener('dragover', (e) => e.preventDefault());
    document.addEventListener('drop', (e) => e.preventDefault());
}

function handleDragOver(e) {
    e.preventDefault();
    uploadArea.classList.add('dragover');
}

function handleDragLeave(e) {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
}

function handleDrop(e) {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        processFile(files[0]);
    }
}

function handleFileSelect(e) {
    const files = e.target.files;
    if (files.length > 0) {
        processFile(files[0]);
    }
}

function processFile(file) {
    console.log('=== PROCESSING FILE ===');
    console.log('File name:', file.name);
    console.log('File type:', file.type);
    console.log('File size:', file.size, 'bytes');

    // Validate file type - be more permissive with MIME types
    const allowedTypes = [
        'image/jpeg', 'image/jpg', 'image/png', 'image/webp',
        'image/bmp', 'image/tiff', 'image/x-ms-bmp'
    ];

    // Also check file extension as fallback
    const fileName = file.name.toLowerCase();
    const allowedExtensions = ['.jpg', '.jpeg', '.png', '.webp', '.bmp', '.tiff', '.tif'];
    const hasValidExtension = allowedExtensions.some(ext => fileName.endsWith(ext));

    if (!allowedTypes.includes(file.type) && !hasValidExtension) {
        console.error('Invalid file type:', file.type, 'for file:', file.name);
        showToast('Please select a valid image file (JPG, PNG, WEBP, BMP, TIFF)', 'error');
        return;
    }

    // Validate file size (16MB)
    if (file.size > 16 * 1024 * 1024) {
        console.error('File too large:', file.size, 'bytes');
        showToast('File size must be less than 16MB', 'error');
        return;
    }

    // Validate file is not empty
    if (file.size === 0) {
        console.error('File is empty');
        showToast('Selected file is empty', 'error');
        return;
    }

    console.log('File validation passed, starting upload...');

    // Show processing state
    showProcessing();

    // Create FormData and upload
    const formData = new FormData();
    formData.append('file', file);

    console.log('FormData created, sending request...');

    // Add timeout to the fetch request
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 60000); // 60 second timeout

    fetch('/upload', {
        method: 'POST',
        body: formData,
        signal: controller.signal
    })
    .then(response => {
        clearTimeout(timeoutId);
        console.log('Upload response received');
        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return response.json();
    })
    .then(data => {
        console.log('Upload response data:', data);

        if (data.success) {
            console.log('Upload successful, showing results...');
            showResults(data);
            showToast('Image processed successfully!', 'success');
        } else {
            console.error('Upload failed:', data.error);
            showError(data.error || 'Processing failed');
        }
    })
    .catch(error => {
        clearTimeout(timeoutId);
        console.error('Upload error:', error);

        if (error.name === 'AbortError') {
            showError('Upload timed out. Please try again with a smaller image.');
        } else if (error.message.includes('Failed to fetch')) {
            showError('Network error: Could not connect to server. Please check if the server is running.');
        } else {
            showError(`Upload failed: ${error.message}`);
        }
    });
}

function showProcessing() {
    console.log('=== SHOWING PROCESSING ===');

    // Check if critical elements exist
    if (!processingSection || !resultsSection || !uploadArea) {
        console.error('❌ Critical elements not found for showProcessing');
        return;
    }

    // Show processing section with smooth transition
    processingSection.style.display = 'block';
    processingSection.classList.add('show');

    // Hide results section
    resultsSection.classList.remove('show');
    setTimeout(() => {
        resultsSection.style.display = 'none';
    }, 300);

    // Hide error section if it exists
    if (errorSection) {
        errorSection.style.display = 'none';
    }

    // Add a subtle visual indicator that upload is still available
    uploadArea.style.opacity = '0.7';
    uploadArea.style.pointerEvents = 'auto'; // Keep it clickable

    // Smooth scroll to processing section
    processingSection.scrollIntoView({ behavior: 'smooth', block: 'center' });
}

function showResults(data) {
    console.log('=== SHOWING RESULTS ===');
    console.log('Results data:', data);

    // Hide processing section with smooth transition
    if (processingSection) {
        processingSection.classList.remove('show');
        setTimeout(() => {
            processingSection.style.display = 'none';
        }, 300);
    }

    // Hide error section if it exists
    if (errorSection) {
        errorSection.style.display = 'none';
    }

    // Restore upload area to full opacity
    if (uploadArea) {
        uploadArea.style.opacity = '1';
    }

    // Reset images
    originalImage.src = '';
    processedImage.src = '';
    originalImage.style.opacity = '1';
    processedImage.style.opacity = '1';

    // Set up image loading handlers
    let originalLoaded = false;
    let processedLoaded = false;

    function checkBothImagesLoaded() {
        if (originalLoaded && processedLoaded) {
            console.log('Both images loaded successfully');
            // Show results section with smooth transition
            resultsSection.style.display = 'block';
            setTimeout(() => {
                resultsSection.classList.add('show');
            }, 50);

            // Smooth scroll to results
            resultsSection.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    }

    // Original image handlers
    originalImage.onload = function() {
        console.log('Original image loaded successfully');
        console.log('Original image dimensions:', this.naturalWidth, 'x', this.naturalHeight);
        originalLoaded = true;
        checkBothImagesLoaded();
    };

    originalImage.onerror = function() {
        console.error('Failed to load original image:', data.original_url);
        showToast('Failed to load original image', 'error');
        showError('Failed to load original image. Please try again.');
    };

    // Processed image handlers
    processedImage.onload = function() {
        console.log('Processed image loaded successfully');
        console.log('Processed image dimensions:', this.naturalWidth, 'x', this.naturalHeight);
        processedLoaded = true;
        checkBothImagesLoaded();
    };

    processedImage.onerror = function() {
        console.error('Failed to load processed image:', data.processed_url);
        showToast('Failed to load processed image', 'error');
        showError('Failed to load processed image. Please try again.');
    };

    // Add cache-busting parameter to URLs
    const timestamp = new Date().getTime();
    const originalUrl = data.original_url + '?t=' + timestamp;
    const processedUrl = data.processed_url + '?t=' + timestamp;

    console.log('Loading original image from:', originalUrl);
    console.log('Loading processed image from:', processedUrl);

    // Reset image opacity before loading
    originalImage.style.opacity = '1';
    processedImage.style.opacity = '1';

    // Set image sources
    originalImage.src = originalUrl;
    processedImage.src = processedUrl;

    // Store download URL
    currentDownloadUrl = data.download_url;

    // Show loading state while images load
    resultsSection.style.display = 'block';
    resultsSection.style.opacity = '0.5';

    // Set timeout in case images don't load
    setTimeout(() => {
        if (!originalLoaded || !processedLoaded) {
            console.warn('Images taking too long to load, showing section anyway');
            resultsSection.style.opacity = '1';

            if (!originalLoaded) {
                console.error('Original image failed to load within timeout');
                showToast('Original image is taking too long to load', 'error');
            }

            if (!processedLoaded) {
                console.error('Processed image failed to load within timeout');
                showToast('Processed image is taking too long to load', 'error');
            }
        }
    }, 10000); // 10 second timeout
}

function showError(message) {
    console.log('=== SHOWING ERROR ===', message);

    // Hide processing and results sections with smooth transitions
    if (processingSection) {
        processingSection.classList.remove('show');
        setTimeout(() => {
            processingSection.style.display = 'none';
        }, 300);
    }
    if (resultsSection) {
        resultsSection.classList.remove('show');
        setTimeout(() => {
            resultsSection.style.display = 'none';
        }, 300);
    }

    // Show error section if it exists
    if (errorSection && errorMessage) {
        errorMessage.textContent = message;
        errorSection.style.display = 'block';
        // Smooth scroll to error section
        errorSection.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }

    // Always show toast notification for errors
    showToast(message, 'error');
}

function hideAllSections() {
    if (uploadSection) uploadSection.style.display = 'none';
    if (processingSection) processingSection.style.display = 'none';
    if (resultsSection) resultsSection.style.display = 'none';
    if (errorSection) errorSection.style.display = 'none';
}

function resetUpload() {
    // Clear file input
    fileInput.value = '';

    // Reset download URL
    currentDownloadUrl = null;

    // Show only upload section
    hideAllSections();
    uploadSection.style.display = 'block';

    // Restore upload area to full opacity
    uploadArea.style.opacity = '1';
    uploadArea.style.pointerEvents = 'auto';

    // Smooth scroll to upload section
    uploadSection.scrollIntoView({ behavior: 'smooth', block: 'center' });

    // Remove dragover class if present
    uploadArea.classList.remove('dragover');
}

function handleDownload() {
    if (currentDownloadUrl) {
        // Create a temporary link and trigger download
        const link = document.createElement('a');
        link.href = currentDownloadUrl;
        link.download = 'slimmed_face.jpg';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        showToast('Download started!', 'success');
    }
}

function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    toast.innerHTML = `
        <div style="display: flex; align-items: center; gap: 10px;">
            <i class="fas ${type === 'error' ? 'fa-exclamation-circle' : 'fa-check-circle'}"></i>
            <span>${message}</span>
        </div>
    `;
    
    toastContainer.appendChild(toast);
    
    // Trigger animation
    setTimeout(() => toast.classList.add('show'), 100);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }, 5000);
}

// Image loading handlers
function handleImageLoad(img) {
    // Ensure the image is fully visible
    img.style.opacity = '1';
    img.style.transition = 'opacity 0.3s ease';
}

// Add loading states to images
originalImage.addEventListener('load', function() {
    console.log('Original image loaded');
    originalLoaded = true;
    handleImageLoad(this);
    checkBothImagesLoaded();
});

processedImage.addEventListener('load', function() {
    console.log('Processed image loaded');
    processedLoaded = true;
    handleImageLoad(this);
    checkBothImagesLoaded();
});

// Check if both images are loaded and show results
function checkBothImagesLoaded() {
    if (originalLoaded && processedLoaded) {
        console.log('Both images loaded, showing results');
        resultsSection.style.opacity = '1';
        resultsSection.classList.add('show');

        // Smooth scroll to results
        setTimeout(() => {
            resultsSection.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }, 300);
    }
}

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // ESC key to reset
    if (e.key === 'Escape') {
        resetUpload();
    }
    
    // Enter key to trigger file selection when upload area is focused
    if (e.key === 'Enter' && document.activeElement === uploadArea) {
        fileInput.click();
    }
});

// Make upload area focusable for accessibility
uploadArea.setAttribute('tabindex', '0');
uploadArea.setAttribute('role', 'button');
uploadArea.setAttribute('aria-label', 'Click or drag and drop to upload an image');

// Add focus styles
uploadArea.addEventListener('focus', function() {
    this.style.outline = '2px solid #667eea';
    this.style.outlineOffset = '2px';
});

uploadArea.addEventListener('blur', function() {
    this.style.outline = 'none';
});

// Comparison Slider Functionality
function initializeComparisonSlider() {
    const slider = document.querySelector('.comparison-slider');
    const sliderHandle = document.querySelector('.slider-handle');
    const afterImage = document.querySelector('.comparison-slider .after');

    if (!slider || !sliderHandle || !afterImage) return;

    let isDragging = false;
    let startX = 0;
    let currentPosition = 50; // Start at 50% (middle)

    // Set initial position
    updateSliderPosition(currentPosition);

    // Mouse events
    sliderHandle.addEventListener('mousedown', startDrag);
    document.addEventListener('mousemove', drag);
    document.addEventListener('mouseup', stopDrag);

    // Touch events for mobile
    sliderHandle.addEventListener('touchstart', startDragTouch, { passive: false });
    document.addEventListener('touchmove', dragTouch, { passive: false });
    document.addEventListener('touchend', stopDrag);

    // Click on slider to move handle
    slider.addEventListener('click', handleSliderClick);

    function startDrag(e) {
        isDragging = true;
        startX = e.clientX;
        sliderHandle.style.cursor = 'grabbing';
        e.preventDefault();
    }

    function startDragTouch(e) {
        isDragging = true;
        startX = e.touches[0].clientX;
        e.preventDefault();
    }

    function drag(e) {
        if (!isDragging) return;

        const rect = slider.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));

        currentPosition = percentage;
        updateSliderPosition(percentage);
        e.preventDefault();
    }

    function dragTouch(e) {
        if (!isDragging) return;

        const rect = slider.getBoundingClientRect();
        const x = e.touches[0].clientX - rect.left;
        const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));

        currentPosition = percentage;
        updateSliderPosition(percentage);
        e.preventDefault();
    }

    function stopDrag() {
        isDragging = false;
        sliderHandle.style.cursor = 'grab';
    }

    function handleSliderClick(e) {
        if (e.target === sliderHandle || sliderHandle.contains(e.target)) return;

        const rect = slider.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));

        currentPosition = percentage;
        updateSliderPosition(percentage);
    }

    function updateSliderPosition(percentage) {
        // Update handle position
        sliderHandle.style.left = `${percentage}%`;

        // Update after image clip path
        afterImage.style.clipPath = `inset(0 ${100 - percentage}% 0 0)`;
    }
}

// Smooth scrolling for better UX
function smoothScrollTo(element) {
    element.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
    });
}

// Add loading animation to buttons
function addButtonLoading(button, originalText) {
    button.disabled = true;
    button.innerHTML = `<i class="fas fa-spinner fa-spin"></i> ${originalText}`;
}

function removeButtonLoading(button, originalText) {
    button.disabled = false;
    button.innerHTML = originalText;
}

// Performance optimization: Lazy load images
function lazyLoadImages() {
    const images = document.querySelectorAll('img[data-src]');
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.removeAttribute('data-src');
                imageObserver.unobserve(img);
            }
        });
    });
    
    images.forEach(img => imageObserver.observe(img));
}

// Initialize lazy loading if supported
if ('IntersectionObserver' in window) {
    document.addEventListener('DOMContentLoaded', lazyLoadImages);
}
